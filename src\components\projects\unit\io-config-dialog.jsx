import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Combobox } from "@/components/ui/combobox";
import { Settings, Zap, Lightbulb, Fan, Thermometer } from "lucide-react";
import {
  getUnitIOSpec,
  getOutputTypes,
  getInputFunctions,
  INPUT_FUNCTIONS,
} from "@/constants";
import { useDatabase } from "@/hooks/useDatabase";
import { useProject } from "@/hooks/useProject";

export function IOConfigDialog({ open, onOpenChange, item = null }) {
  const { currentProject } = useProject();
  const { db } = useDatabase();

  const [activeTab, setActiveTab] = useState("input");
  const [lightingItems, setLightingItems] = useState([]);
  const [airconItems, setAirconItems] = useState([]);
  const [inputConfigs, setInputConfigs] = useState([]);
  const [outputConfigs, setOutputConfigs] = useState([]);

  // Get I/O specifications for the unit
  const ioSpec = item?.type ? getUnitIOSpec(item.type) : null;
  const outputTypes = item?.type ? getOutputTypes(item.type) : [];

  // Load lighting and aircon data
  useEffect(() => {
    if (!currentProject?.id || !db) return;

    try {
      const lighting = db.getLightingItems(currentProject.id);
      const aircon = db.getAirconItems(currentProject.id);

      setLightingItems(lighting);
      setAirconItems(aircon);
    } catch (error) {
      console.error("Failed to load lighting/aircon data:", error);
    }
  }, [currentProject?.id, db]);

  // Initialize input/output configurations
  useEffect(() => {
    if (!ioSpec) return;

    // Initialize input configurations
    const inputs = [];
    for (let i = 0; i < ioSpec.inputs; i++) {
      inputs.push({
        index: i,
        name: `Input ${i + 1}`,
        lightingId: null,
        functionValue: 0, // Default to "Unused"
      });
    }
    setInputConfigs(inputs);

    // Initialize output configurations
    const outputs = [];
    let outputIndex = 0;

    outputTypes.forEach(({ type, count }) => {
      for (let i = 0; i < count; i++) {
        outputs.push({
          index: outputIndex++,
          name: `${getOutputLabel(type)} ${i + 1}`,
          type: type,
          deviceId: null,
        });
      }
    });
    setOutputConfigs(outputs);
  }, [ioSpec, outputTypes]);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    // TODO: Implement I/O configuration save logic
    console.log("I/O Config for unit:", item);
    console.log("Input configs:", inputConfigs);
    console.log("Output configs:", outputConfigs);
    handleClose();
  };

  const getOutputIcon = (type) => {
    switch (type) {
      case "relay":
        return <Zap className="h-4 w-4" />;
      case "dimmer":
        return <Lightbulb className="h-4 w-4" />;
      case "ao":
        return <Fan className="h-4 w-4" />;
      case "ac":
        return <Thermometer className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getOutputLabel = (type) => {
    switch (type) {
      case "relay":
        return "Relay";
      case "dimmer":
        return "Dimmer";
      case "ao":
        return "Analog Output";
      case "ac":
        return "Air Conditioner";
      default:
        return type;
    }
  };

  // Helper functions for input/output configuration
  const handleInputLightingChange = (inputIndex, lightingId) => {
    setInputConfigs((prev) =>
      prev.map((config) =>
        config.index === inputIndex ? { ...config, lightingId } : config
      )
    );
  };

  const handleInputFunctionChange = (inputIndex, functionValue) => {
    setInputConfigs((prev) =>
      prev.map((config) =>
        config.index === inputIndex ? { ...config, functionValue } : config
      )
    );
  };

  const handleOutputDeviceChange = (outputIndex, deviceId) => {
    setOutputConfigs((prev) =>
      prev.map((config) =>
        config.index === outputIndex ? { ...config, deviceId } : config
      )
    );
  };

  // Prepare combobox options
  const lightingOptions = lightingItems.map((item) => ({
    value: item.id.toString(),
    label: `${item.name || "Unnamed"} (${item.address})`,
  }));

  const airconOptions = airconItems.map((item) => ({
    value: item.id.toString(),
    label: `${item.name || "Unnamed"} (${item.address})`,
  }));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            I/O Configuration
          </DialogTitle>
          <DialogDescription>
            Configure input/output settings for {item?.type || "unit"}:{" "}
            {item?.serial_no || "N/A"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {ioSpec ? (
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="input">Input Configuration</TabsTrigger>
                <TabsTrigger value="output">Output Configuration</TabsTrigger>
              </TabsList>

              {/* Input Tab */}
              <TabsContent value="input" className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Input Configuration</h3>
                  <Badge variant="secondary" className="ml-auto">
                    {ioSpec.inputs} Inputs
                  </Badge>
                </div>

                {inputConfigs.length > 0 ? (
                  <div className="space-y-3">
                    {inputConfigs.map((config) => {
                      const availableFunctions = getInputFunctions(
                        item?.type,
                        config.index
                      );
                      const functionOptions = availableFunctions.map(
                        (func) => ({
                          value: func.value.toString(),
                          label: func.label,
                        })
                      );

                      return (
                        <div
                          key={config.index}
                          className="grid grid-cols-3 gap-4 p-4 border rounded-lg"
                        >
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">
                              {config.name}
                            </Label>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm text-muted-foreground">
                              Lighting
                            </Label>
                            <Combobox
                              options={lightingOptions}
                              value={config.lightingId?.toString() || ""}
                              onValueChange={(value) =>
                                handleInputLightingChange(
                                  config.index,
                                  value ? parseInt(value) : null
                                )
                              }
                              placeholder="Select lighting..."
                              emptyText="No lighting found"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm text-muted-foreground">
                              Function
                            </Label>
                            <Combobox
                              options={functionOptions}
                              value={config.functionValue.toString()}
                              onValueChange={(value) =>
                                handleInputFunctionChange(
                                  config.index,
                                  parseInt(value)
                                )
                              }
                              placeholder="Select function..."
                              emptyText="No functions available"
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <p>No inputs available for this unit type.</p>
                  </div>
                )}
              </TabsContent>

              {/* Output Tab */}
              <TabsContent value="output" className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">
                    Output Configuration
                  </h3>
                  <Badge variant="secondary" className="ml-auto">
                    {ioSpec.totalOutputs} Outputs
                  </Badge>
                </div>

                {outputConfigs.length > 0 ? (
                  <div className="space-y-3">
                    {outputConfigs.map((config) => {
                      const isAircon = config.type === "ac";
                      const deviceOptions = isAircon
                        ? airconOptions
                        : lightingOptions;

                      return (
                        <div
                          key={config.index}
                          className="grid grid-cols-2 gap-4 p-4 border rounded-lg"
                        >
                          <div className="space-y-2">
                            <Label className="text-sm font-medium flex items-center gap-2">
                              {getOutputIcon(config.type)}
                              {config.name}
                            </Label>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm text-muted-foreground">
                              {isAircon ? "Air Conditioner" : "Lighting"}
                            </Label>
                            <Combobox
                              options={deviceOptions}
                              value={config.deviceId?.toString() || ""}
                              onValueChange={(value) =>
                                handleOutputDeviceChange(
                                  config.index,
                                  value ? parseInt(value) : null
                                )
                              }
                              placeholder={`Select ${
                                isAircon ? "aircon" : "lighting"
                              }...`}
                              emptyText={`No ${
                                isAircon ? "aircon" : "lighting"
                              } found`}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    <p>No outputs available for this unit type.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <div className="text-center text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No I/O Specifications</p>
              <p className="text-sm">
                Unable to load I/O specifications for this unit type.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Configuration</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
