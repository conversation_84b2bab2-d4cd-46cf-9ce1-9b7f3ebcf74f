import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Settings, Zap, Lightbulb, Fan, Thermometer } from "lucide-react";
import { getUnitIOSpec, getOutputTypes } from "@/constants";

export function IOConfigDialog({ open, onOpenChange, item = null }) {
  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = () => {
    // TODO: Implement I/O configuration save logic
    console.log("I/O Config for unit:", item);
    handleClose();
  };

  // Get I/O specifications for the unit
  const ioSpec = item?.type ? getUnitIOSpec(item.type) : null;
  const outputTypes = item?.type ? getOutputTypes(item.type) : [];

  const getOutputIcon = (type) => {
    switch (type) {
      case "relay":
        return <Zap className="h-4 w-4" />;
      case "dimmer":
        return <Lightbulb className="h-4 w-4" />;
      case "ao":
        return <Fan className="h-4 w-4" />;
      case "ac":
        return <Thermometer className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  const getOutputLabel = (type) => {
    switch (type) {
      case "relay":
        return "Relay";
      case "dimmer":
        return "Dimmer";
      case "ao":
        return "Analog Output";
      case "ac":
        return "Air Conditioner";
      default:
        return type;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            I/O Configuration
          </DialogTitle>
          <DialogDescription>
            Configure input/output settings for {item?.type || "unit"}:{" "}
            {item?.serial_no || "N/A"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4 space-y-6">
          {/* Unit Information */}
          <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
            <div>
              <p className="text-sm font-medium">Unit Type</p>
              <p className="text-sm text-muted-foreground">
                {item?.type || "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">Serial No</p>
              <p className="text-sm text-muted-foreground">
                {item?.serial_no || "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">IP Address</p>
              <p className="text-sm text-muted-foreground">
                {item?.ip_address || "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">ID CAN</p>
              <p className="text-sm text-muted-foreground">
                {item?.id_can || "N/A"}
              </p>
            </div>
          </div>

          {/* I/O Specifications */}
          {ioSpec ? (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">I/O Specifications</h3>

              {/* Inputs */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span className="font-medium">Inputs</span>
                  <Badge variant="secondary">{ioSpec.inputs}</Badge>
                </div>
                {ioSpec.inputs === 0 && (
                  <p className="text-sm text-muted-foreground ml-6">
                    No inputs available
                  </p>
                )}
              </div>

              {/* Outputs */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span className="font-medium">Outputs</span>
                  <Badge variant="secondary">{ioSpec.totalOutputs}</Badge>
                </div>

                {outputTypes.length > 0 ? (
                  <div className="ml-6 space-y-2">
                    {outputTypes.map((output, index) => (
                      <div key={index} className="flex items-center gap-2">
                        {getOutputIcon(output.type)}
                        <span className="text-sm">
                          {getOutputLabel(output.type)}
                        </span>
                        <Badge variant="outline">{output.count}</Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground ml-6">
                    No outputs available
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No I/O Specifications</p>
              <p className="text-sm">
                Unable to load I/O specifications for this unit type.
              </p>
            </div>
          )}

          {/* Configuration Interface Placeholder */}
          <div className="border-t pt-4">
            <div className="text-center text-muted-foreground">
              <p className="text-sm font-medium mb-2">
                Configuration Interface
              </p>
              <p className="text-sm">
                Detailed I/O configuration interface will be implemented here
                based on the unit specifications above.
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Configuration</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
